DESCRIPTION = "The Carbon metric processing daemons"
HOMEPAGE = "https://github.com/graphite-project/carbon"
LICENSE = "Apache-2.0"
SECTION = "devel/python"

LIC_FILES_CHKSUM="file://LICENSE;md5=3b83ef96387f14655fc854ddc3c6bd57"

SRCREV = "7869ca2587526d113b0404bbcea8b570c79a3d53"
SRC_URI[sha256sum] = ""
SRC_URI = "git://github.com/graphite-project/carbon.git;branch=master;protocol=https \
           file://0001-fix-remove-examples-files-and-init.d-script.patch \
           file://0002-fix-Add-i2R-configuration.patch \
           file://conf/carbon.conf \
           file://conf/storage-schemas.conf \
           file://conf/storage-aggregation.conf \
           file://service/carbon-cache.service \
          "

S = "${WORKDIR}/git"

RDEPENDS:${PN} = " python3 \
                   python3-resource \
                   python3-twisted \
                   python3-whisper \
                   python3-cachetools \
                   python3-urllib3 \
                   python3-syslog \
                  "

inherit allarch setuptools3 systemd

# Required otherwise files are installed in /usr/lib/python3.12/site-packages/opt/graphite/lib/carbon
# When doing enabling GRAPHITE_NO_PREFIX files are correctly placed in /usr/lib/python3.12/site-packages/carbon
export GRAPHITE_NO_PREFIX="True"

REMOVE_FEATURE = "amqp0-8.xml amqp_listener.py amqp_publisher.py"

do_configure:prepend() {
    bbnote "Remove amqp feature, python3-txAMQP is not present."
    for file in $REMOVE_FEATURE
    do
        rm -f ${S}/lib/$file
    done
}

do_install:append() {
    # Install configuration files
    install -d ${D}${sysconfdir}/carbon
    cp -r ${WORKDIR}/conf/*.conf ${D}${sysconfdir}/carbon/

    # Recursively create folders
    install -d ${D}/opt/graphite/storage/whisper

    # Install systemd service
    if ${@bb.utils.contains('DISTRO_FEATURES', 'systemd', 'true', 'false', d)}; then
       install -d ${D}${systemd_system_unitdir}
       install -m 0600 ${WORKDIR}/service/carbon-cache.service ${D}${systemd_system_unitdir}/
    fi
}

FILES:${PN} += " \
    ${systemd_system_unitdir}/carbon-cache.service \
    /opt/graphite/storage/whisper \
"

SYSTEMD_SERVICE:${PN} = "carbon-cache.service"
SYSTEMD_AUTO_ENABLE:${PN} = "disable"

CVE_PRODUCT = "graphite"
BBCLASSEXTEND = "native"
