From 372512b387753f9352be81414eb46374e04c399a Mon Sep 17 00:00:00 2001
From: <PERSON><PERSON> <<EMAIL>>
Date: Thu, 28 Nov 2024 14:14:21 +0100
Subject: [PATCH 2/3] fix: remove examples files and init.d script

---
 setup.py | 10 +++-------
 1 file changed, 3 insertions(+), 7 deletions(-)

diff --git a/setup.py b/setup.py
index 4d6a5f4..a869142 100644
--- a/setup.py
+++ b/setup.py
@@ -58,18 +58,14 @@ else:
   setup_kwargs = dict()


-storage_dirs = [ ('storage/ceres/dummy.txt', []), ('storage/whisper/dummy.txt',[]),
-                 ('storage/lists',[]), ('storage/log/dummy.txt',[]),
-                 ('storage/rrd/dummy.txt',[]) ]
-conf_files = [ ('conf', glob('conf/*.example')) ]
+storage_dirs = []
+conf_files = []

 install_files = storage_dirs + conf_files

 # Let's include redhat init scripts, despite build platform
 # but won't put them in /etc/init.d/ automatically anymore
-init_scripts = [ ('examples/init.d', ['distro/redhat/init.d/carbon-cache',
-                                      'distro/redhat/init.d/carbon-relay',
-                                      'distro/redhat/init.d/carbon-aggregator']) ]
+init_scripts = [ ]
 install_files += init_scripts

 def read(fname):
--
2.34.1

