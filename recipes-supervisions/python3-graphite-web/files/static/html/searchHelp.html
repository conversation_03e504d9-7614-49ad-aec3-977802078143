<!-- Copyright 2008 Orbitz WorldWide

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

   http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License. -->

<h1>Searching Graphite</h1><br>

<p>The search box may contain multiple search strings separated by spaces. Queries with multiple strings are treated as an inclusive search, returning results that match <em>either</em> string. For example,</p>

<p>
<pre>
lfs busy
</pre>
</p>

<p>Will match metric names that contain <em>either</em> the word "lfs" <em>or</em> the word "busy".</p>

<p>Advanced users may note that every search string can be a regular expression.</p>
