/* You should not need to change stuff in this file.
   If something is missing from dashboard-default.css
  that belongs there, please let graphite-dev know.
*/
div.graph-container {
  float: left;
  margin: 7px;
  margin-right: 0;
}

div.graph-overlay {
  position: relative;
  visibility: hidden;
}

div.overlay-close-button {
  position: absolute;
  top: -5px;
  right: -5px;
  background-color: #faa;
  color: #922;
  font-family: monospace;
  font-size: 8pt;
  font-weight: bold;
  text-align: center;
  width: 15px;
  height: 15px;
  border-radius:         5px;
  -moz-border-radius:    5px;
  -webkit-border-radius: 5px;
  -khtml-border-radius:  5px;
}

img.graph-img {
  visibility: visible;
  padding: 3px;
  border-radius:         5px;
  -moz-border-radius:    5px;
  -webkit-border-radius: 5px;
  -khtml-border-radius:  5px;
}

img.graph-img-loading {
  visibility: visible;
  padding: 3px;
  border-radius:         5px;
  -moz-border-radius:    5px;
  -webkit-border-radius: 5px;
  -khtml-border-radius:  5px;
  background-color: rgb(140, 140, 255);
}

.graph-over img {
  background-color: rgb(140, 140, 255);
}

.graph-over div.overlay-close-button {
  visibility: visible;
}

.selectable, .selectable * {
  user-select: text !important;
  -moz-user-select: text !important;
  -khtml-user-select: text !important;
  -webkit-user-select: text !important;
}

.x-unselectable, .x-unselectable * {
  user-select: text !important;
  -moz-user-select: text !important;
  -khtml-user-select: text !important;
  -webkit-user-select: text !important;
}

.x-list-selected {
  background-color: rgb(180, 180, 220);
}

/* Customize the size of the split bar toggle button */
.x-layout-split {
  height: 10px;
  width: 10px;
}

.x-layout-cmini-west, .x-layout-cmini-east {
  width: 10px !important;
}

.x-layout-split-north .x-layout-mini {
  height: 10px;
  background-image: url(../img/mini-top2.gif);
}

.x-layout-split-south .x-layout-mini {
  height: 10px !important;
  background-image: url(../img/mini-bottom2.gif);
}

.x-layout-split-east .x-layout-mini {
  width: 10px !important;
}

.x-layout-cmini-north {
  height: 10px !important;
}

.x-layout-cmini-north .x-layout-mini {
  height: 10px !important;
  background-image: url(../img/mini-bottom2.gif);
}

.x-layout-cmini-south .x-layout-mini {
  height: 10px !important;
  background-image: url(../img/mini-top2.gif);
}

.x-layout-cmini-west .x-layout-mini {
  width: 10px !important;
}

/* DnD classes */
.x-dd-drop-ok {
}

.x-dd-drop-nodrop {
}

#merge {
  display: none;
  height: 16;
  text-align: center;
  background-color: rgb(200, 255, 200);
  color: rgb(20, 80, 20);
  font-size: 9pt;
  font-family: sans-serif;
  font-weight: bold;
}
