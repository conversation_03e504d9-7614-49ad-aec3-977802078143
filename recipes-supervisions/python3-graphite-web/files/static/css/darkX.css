.overlay_darkX {
	background-color: #85BBEF;
	filter:alpha(opacity=60);
	-moz-opacity: 0.6;
	opacity: 0.6;
}

.darkX_nw {
	background: transparent url(darkX/titlebar-left-focused.png) no-repeat 0 0;			
  width:6px;
  height:21px;
}
.darkX_n {
  background: transparent url(darkX/titlebar-mid-focused.png) repeat-x 0 0;			
  height:21px;
}
.darkX_ne {
  background: transparent url(darkX/titlebar-right-focused.png) no-repeat 0 0;			
  width:6px;	  
  height:21px;
}
.darkX_w {
  background: transparent url(darkX/frame-left-focused.png) repeat-y top left;			
  width:3px;
}

.darkX_e {
  background: transparent url(darkX/frame-right-focused.png) repeat-y top right;			
  width:3px;	  
}

.darkX_sw {
  background: transparent url(darkX/frame-bottom-left-focused.png) no-repeat 0 0;			
  width:5px;
  height:3px;
}
.darkX_s {
  background: transparent url(darkX/frame-bottom-mid-focused.png) repeat-x 0 0;			
  height:3px;
}
.darkX_se, .darkX_sizer {
  background: transparent url(darkX/frame-bottom-right-focused.png) no-repeat 0 0;			
  width:5px;
  height:3px;
}

.darkX_sizer {
	cursor:se-resize;	
}

.darkX_close {
	width: 21px;
	height: 21px;
	background: transparent url(darkX/button-close-focused.png) no-repeat 0 0;			
	position:absolute;
	top:0px;
	right:5px;
	cursor:pointer;
	z-index:1000;
}

.darkX_minimize {
	width: 21px;
	height: 21px;
	background: transparent url(darkX/button-minimize-focused.png) no-repeat 0 0;			
	position:absolute;
	top:0px;
	right:26px;
	cursor:pointer;
	z-index:1000;
}

.darkX_maximize {
	width: 21px;
	height: 21px;
	background: transparent url(darkX/button-maximize-focused.png) no-repeat 0 0;			
	position:absolute;
	top:0px;
	right:47px;
	cursor:pointer;
	z-index:1000;
}


.darkX_title {
	float:left;
	height:14px;
	font-size:12px;
	text-align:center;
	margin-top:2px;
	width:100%;
	color:#FFF;
}

.darkX_content {
	overflow:auto;
	color: #E6DF2A;
	font-family: Tahoma, Arial, sans-serif;
	font-size: 14px;
	background:#5E5148;
}


/* FOR IE */
* html .darkX_minimize {
	background-color: transparent;
	background-image: none;
	filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(src="../themes/darkX/button-minimize-focused.png", sizingMethod="crop");
}

* html .darkX_maximize {
	background-color: transparent;
	background-image: none;
	filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(src="../themes/darkX/button-maximize-focused.png", sizingMethod="scale");
}

* html .darkX_close {
	background-color: transparent;
	background-image: none;
	filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(src="../themes/darkX/button-close-focused.png", sizingMethod="crop");
}
