commit d92bccc29882faf421a62761fa8436fce5072b64
Author: ALLAN ELKAIM <<EMAIL>>
Date:   Thu Jan 9 13:06:24 2025 +0100

    Update whitenoise version check

diff --git a/webapp/graphite/app_settings.py b/webapp/graphite/app_settings.py
index a037aa89..5a281b9a 100644
--- a/webapp/graphite/app_settings.py
+++ b/webapp/graphite/app_settings.py
@@ -20,18 +20,21 @@ try:
     import raven
 except ImportError:
     raven = None
+
+from importlib.metadata import version
 try:
     import whitenoise
 except ImportError:
     whitenoise = False
 else:
-    whitenoise_version = tuple(map(
-            int, getattr(whitenoise, '__version__', '0').split('.')))
+    whitenoise_version = version('whitenoise')
+    whitenoise_version_tuple = tuple(map(int, whitenoise_version.split('.')))
     # Configure WhiteNoise < 3.2 from wsgi.py
     # http://whitenoise.evans.io/en/stable/changelog.html#v4-0
-    if whitenoise_version < (3, 2):
+    if whitenoise_version_tuple < (3, 2):
         whitenoise = False

+
 #Django settings below, do not touch!
 APPEND_SLASH = False
 TEMPLATE_DEBUG = False
diff --git a/webapp/graphite/wsgi.py b/webapp/graphite/wsgi.py
index bacb6737..7ea50015 100644
--- a/webapp/graphite/wsgi.py
+++ b/webapp/graphite/wsgi.py
@@ -14,20 +14,21 @@ from django.core.wsgi import get_wsgi_application

 application = get_wsgi_application()

+from importlib.metadata import version
 try:
     import whitenoise
 except ImportError:
     whitenoise = False
 else:
-    whitenoise_version = tuple(map(
-            int, getattr(whitenoise, '__version__', '0').split('.')))
+    whitenoise_version = version('whitenoise')
+    whitenoise_version_tuple = tuple(map(int, whitenoise_version.split('.')))
     # WhiteNoise < 2.0.1 does not support Python 2.6
     if sys.version_info[:2] < (2, 7):
-        if whitenoise_version < (2, 0, 1):
+        if whitenoise_version_tuple < (2, 0, 1):
             whitenoise = False
     # Configure WhiteNoise >= 3.2 as middleware from app_settings.py
     # http://whitenoise.evans.io/en/stable/changelog.html#v4-0
-    if whitenoise_version >= (3, 2):
+    if whitenoise_version_tuple >= (3, 2):
         whitenoise = False

 if whitenoise:
