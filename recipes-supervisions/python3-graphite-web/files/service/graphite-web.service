[Unit]
Description=[i2R] graphite-web service
Requires=graphite-web.socket carbon-cache.service
After=carbon-cache.service

[Service]
ExecStart=/usr/bin/gunicorn wsgi --pythonpath=/usr/lib/python3.12/site-packages/graphite --bind 127.0.0.1:8080
Restart=on-failure
#User=graphite
#Group=graphite
ExecReload=/bin/kill -s HUP $MAINPID
ExecStop=/bin/kill -s TERM $MAINPID
PrivateTmp=true

[Install]
WantedBy=multi-user.target
