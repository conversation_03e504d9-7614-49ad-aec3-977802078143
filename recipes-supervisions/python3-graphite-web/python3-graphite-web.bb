DESCRIPTION = "Graphite-web - Frontend for the Graphite monitoring tool"
HOMEPAGE = "https://github.com/graphite-project/graphite-web"
LICENSE = "Apache-2.0"
SECTION = "devel/python"

LIC_FILES_CHKSUM="file://LICENSE;md5=657e76d72a5c69fea075f04b42e76893"

SRCREV = "55adbb6fd80a3dcd089a2c4458c71af01f191c9b"
SRC_URI[sha256sum] = ""
SRC_URI = "git://github.com/graphite-project/graphite-web.git;branch=master;protocol=https \
           file://local_settings.py \
           file://static \
           file://graphite.db \
           file://nginx-conf/graphite.conf \
           file://service/graphite-web.service \
           file://service/graphite-web.socket \
           file://patches/0001-Update-whitenoise-version-checks.patch \
          "

S = "${WORKDIR}/git"

RDEPENDS:${PN} = " \
    bash \
    nginx \
    python3 \
    python3-resource \
    python3-django \
    python3-memcached \
    python3-django-tagging \
    python3-gunicorn \
    python3-pytz \
    python3-pyparsing \
    python3-cairocffi \
    python3-whisper \
    python3-whitenoise \
    python3-urllib3 \
    python3-six \
    python3-syslog \
    tzdata \
"

inherit allarch setuptools3 systemd

# Required otherwise files are installed in /usr/lib/python3.12/site-packages/opt/graphite/webapp/graphite/
# When doing enabling GRAPHITE_NO_PREFIX files are correctly placed in /usr/lib/python3.12/site-packages/graphite
export GRAPHITE_NO_PREFIX="True"

# Remove conf samples and examples
do_configure:prepend() {
    rm -rf ${S}/conf
    rm -rf ${S}/examples
}

# Manually move webapp from /usr/webapp to /opt/graphite/webapp
# Note: I couldn't find a way to do so properly using the setup.py arguments
do_install:append() {
    # Recursively create folders
    install -d ${D}/opt/graphite/storage/log/webapp

    # Move webapp to /opt/graphite/
    mv ${D}/usr/webapp/ ${D}/opt/graphite/

    # Copy local_settings.py
    cp -r ${WORKDIR}/local_settings.py ${D}${PYTHON_SITEPACKAGES_DIR}/graphite

    # Copy default graphite.db
    # NOTE: the default graphite.db has been generated using this command on the target: `PYTHONPATH=/usr/lib/python3.12/site-packages/graphite django-admin migrate --settings=graphite.settings`
    cp -r ${WORKDIR}/graphite.db ${D}/opt/graphite/storage/

    # Copy the static folder
    # NOTE: the static folder was generated using this command on the target: `PYTHONPATH=/usr/lib/python3.12/site-packages/graphite django-admin collectstatic --settings=graphite.settings`
    cp -r ${WORKDIR}/static ${D}/opt/graphite/static

    # Copy service files# Install systemd service
    if ${@bb.utils.contains('DISTRO_FEATURES', 'systemd', 'true', 'false', d)}; then
       install -d ${D}${systemd_system_unitdir}
       install -m 0600 ${WORKDIR}/service/graphite-web.service ${D}${systemd_system_unitdir}/
       install -m 0600 ${WORKDIR}/service/graphite-web.socket ${D}${systemd_system_unitdir}/
    fi

    # Create nginx folder to store the configuration file
    install -d ${D}${sysconfdir}/nginx/sites-available
    install -m 0644 ${WORKDIR}/nginx-conf/graphite.conf ${D}${sysconfdir}/nginx/sites-available/

    # Enable graphite.conf server
    install -d ${D}${sysconfdir}/nginx/sites-enabled
    ln -sf /etc/nginx/sites-available/graphite.conf ${D}${sysconfdir}/nginx/sites-enabled/graphite.conf
}

FILES:${PN} += "\
   /opt/graphite \
   ${systemd_system_unitdir}/graphite-web.service \
   ${systemd_system_unitdir}/graphite-web.socket \
"

SYSTEMD_SERVICE:${PN} = "graphite-web.service"
SYSTEMD_AUTO_ENABLE:${PN} = "disable"

CVE_PRODUCT = "graphite"
BBCLASSEXTEND = "native"
