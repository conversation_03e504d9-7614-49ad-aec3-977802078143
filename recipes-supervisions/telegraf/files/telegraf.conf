[[inputs.cpu]]
  percpu = true
  totalcpu = true
  collect_cpu_time = false
  report_active = false
 
[[inputs.mem]]

[[inputs.chrony]]
  metrics = ["tracking"]

[[inputs.file]]
  files = ["/var/lib/i2r/metrics/modem-signal.txt"]
  data_format = "value"
  data_type = "integer"
  name_override = "modem_signal_level"
  interval = "15m"
 
[[outputs.graphite]]
  servers = ["localhost:2003"]
  prefix = "i2r"
  template = "host.tags.measurement.field"

# InfluxDB line protocol file output
[[outputs.file]]
  files = ["/var/lib/i2r/metrics/telegraf-influxdb.log"]
  data_format = "influx"
  # Rotate files when they reach 10MB
  rotation_max_size = "10MB"
  # Keep up to 5 rotated files
  rotation_max_archives = 5