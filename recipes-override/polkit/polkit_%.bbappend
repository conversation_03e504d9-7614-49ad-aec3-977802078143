# Appended recipe : meta-openembedded/meta-oe/recipes-extended/polkit

# Install rules for user i2r-system
FILESEXTRAPATHS:prepend := "${THISDIR}/files:"

SRC_URI += "file://10-i2r-system.rules"

FILES:${PN} += "${datadir}/polkit-1/rules.d/10-i2r-system.rules"

do_install:append() {
    install -d ${D}${datadir}/polkit-1/rules.d/
    install -m 0644 ${WORKDIR}/10-i2r-system.rules ${D}${datadir}/polkit-1/rules.d/
}
