/*
 *  pslash - a lightweight framebuffer splashscreen for embedded devices.
 *
 *  Copyright (c) 2012 sleep(5) ltd
 *  Author: <PERSON> <<EMAIL>>
 *
 *  This program is free software; you can redistribute it and/or modify
 *  it under the terms of the GNU General Public License as published by
 *  the Free Software Foundation; either version 2, or (at your option)
 *  any later version.
 *
 *  This program is distributed in the hope that it will be useful,
 *  but WITHOUT ANY WARRANTY; without even the implied warranty of
 *  MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
 *  GNU General Public License for more details.
 *
 */

#ifndef _HAVE_PSPLASH_COLORS_H
#define _HAVE_PSPLASH_COLORS_H

/* This is the overall background color */
#define PSPLASH_BACKGROUND_COLOR 0xFF,0xFF,0xFF

/* This is the color of any text output */
#define PSPLASH_TEXT_COLOR 0x00,0x00,0x00

/* This is the color of the progress bar indicator */
#define PSPLASH_BAR_COLOR 0x00,0x00,0x00

/* This is the color of the progress bar background */
#define PSPLASH_BAR_BACKGROUND_COLOR 0xFF,0xFF,0xFF

#endif