JVM_RDEPENDS:aarch64 = " \
  alsa-lib (>= 0.9) \
  freetype (>= 2.13) \
  glibc (>= 2.34) \
  zlib (>= 1.1.4) \
"
JVM_RDEPENDS:x86-64 = " \
  alsa-lib (>= 0.9) \
  freetype (>= 2.13) \
  glibc (>= 2.34) \
  zlib (>= 1.1.4) \
"

libdir_jvm = "${JAVA_INSTALL_DIR}"

do_install:append() {
  bbnote "Append to install remove graphic lib from JRE to headless installation."
  for i in libsplashscreen.so libawt_xawt.so libjawt.so
  do
    rm -f ${D}${libdir_jvm}/lib/$i
    bbnote "delete ${D}${libdir_jvm}/lib/$i"
  done
}
