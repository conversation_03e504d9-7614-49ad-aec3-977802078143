SRC_URI += "http://crl-enedis.enedis.fr/ac/autorite_racine_groupe_edf.cer;sha256sum=8add8dcbff778ca77dd03a5e0291d97d1bf5c4f1572d1458fb13044870b19ac1 \
           http://crl-enedis.enedis.fr/ac/autorite_intermediaire_enedis.cer;sha256sum=fe596754d161f0cff95ebc09546b75334fffcdf5b70195e35b637ccd8a035d1a \
           http://crl-enedis.enedis.fr/ac/autorite_infrastructure_enedis.cer;sha256sum=008ccf32a46858947a75ed0f20b89098042c1ef182bca6ba614ec6257ce0ec92 \
          "

JVM_RDEPENDS:aarch64 = " \
  alsa-lib (>= 0.9) \
  freetype (>= 2.13) \
  glibc (>= 2.34) \
  zlib (>= 1.1.4) \
"
JVM_RDEPENDS:x86-64 = " \
  alsa-lib (>= 0.9) \
  freetype (>= 2.13) \
  glibc (>= 2.34) \
  zlib (>= 1.1.4) \
"

python __anonymous() {
    d.delVarFlag('do_configure', 'noexec')
}

do_configure() {
    export JAVA_HOME=${WORKDIR}/${BP}
    $JAVA_HOME/bin/java -version

    cd ${WORKDIR}
    bbnote "Add Enedis ACs to cacerts truststore in ${JAVA_HOME}/lib/security/cacerts"
    for ac in autorite_racine_groupe_edf autorite_intermediaire_enedis autorite_infrastructure_enedis
    do
      "$JAVA_HOME/bin/keytool" -import -no-prompt -alias "${ac}" -cacerts -file "${ac}.cer" -storepass changeit || true
    done
}

do_install:append() {
  bbnote "Append to install remove graphic lib from JRE to headless installation."
  for i in libsplashscreen.so libawt_xawt.so libjawt.so
  do
    rm -f ${D}${libdir_jdk}/lib/$i
    bbnote "delete ${D}${libdir_jdk}/lib/$i"
  done
}
