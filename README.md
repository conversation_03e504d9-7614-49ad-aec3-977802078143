This README file contains information on the contents of the meta-i2r layer.

* recipes-core
  * i2r-app recipe
    * Recipe that will fetch the correct binaries and its dependencies from artifactory and package them
  * i2r-usersgroup
    * Recipe that will create users and groups "i2r-lib" and "i2r-system" with proper unix permissions
  * packagegroups
    * Define groups of package that often are used together, this makes it easier to include inside an image.
  * sqlite-jdbc
    * Recipe that will fetch sqlite-jdbc library from artifactory and remove non aarch64 binaries to make the .jar lighter

* recipes-override
  * openjdk-21
    * Recipe to compile openjdk-21
  * psplash
    * Recipe to customize the boot image (doesn't seem to be working on raspberry pi systems)
* recipes-supervision
  * python3-whisper
    * Recipe for whisper database
* recipes-test
  * hal-mock
    * Recipe that compiles and packages the mocked HAL

With all of the above items, we can easilly produce a linux image using Yocto by simply including the i2r-layer.

Please see the corresponding sections below for details.

Dependencies
============

  URI: <first dependency>
  branch: <branch name>

  URI: <second dependency>
  branch: <branch name>

  .
  .
  .

Patches
=======

Please submit any patches against the meta-i2r layer to the xxxx mailing list (<EMAIL>)
and cc: the maintainer:

Maintainer: XXX YYYYYY <<EMAIL>>

Table of Contents
=================

  I. Adding the meta-i2r layer to your build
 II. Misc


I. Adding the meta-i2r layer to your build
=================================================

Run 'bitbake-layers add-layer meta-i2r'

II. Misc
========

--- replace with specific information about the meta-i2r layer ---
