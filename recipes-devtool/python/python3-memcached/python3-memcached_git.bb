SUMMARY = "Pure python memcached client"
DESCRIPTION = "\
  This software is a 100% Python interface to the memcached memory cache daemon. \
  It is the client side software which allows storing values in one or more, possibly remote, \
  memcached servers. Search google for memcached for more information."
HOMEPAGE = "https://pypi.python.org/pypi/python-memcached/"
SECTION = "devel/python"
LICENSE = "PSF-2.0"
LIC_FILES_CHKSUM = "file://PSF.LICENSE;md5=03e566532476d47eb679b08f0c948240"

SRC_URI = "git://github.com/linsomniac/python-memcached.git;branch=master;protocol=https"
SRCREV = "93456274ceca8502bb9b3695ee18dfe1deb47f29"

S = "${WORKDIR}/git"

inherit setuptools3

RDEPENDS:${PN} += " \
    ${PYTHON_PN}-six \
    ${PYTHON_PN}-pickle \
    "
