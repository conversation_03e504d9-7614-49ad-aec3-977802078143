SUMMARY = "Python CFFI-based cairo bindings for graphics"
DESCRIPTION = "Cairocffi is a CFFI-based drop-in replacement for Pycairo."
HOMEPAGE = "https://github.com/Kozea/cairocffi"
LICENSE = "BSD-3-Clause"
LIC_FILES_CHKSUM = "file://LICENSE;md5=e7c7639c2f7e3d6103414416614bfaac"

SRC_URI = "git://github.com/Kozea/cairocffi.git;branch=main;protocol=https"
SRCREV = "0f45d6a42a352d4255a8ffec4b77ae648f19d654"

inherit allarch python_setuptools_build_meta

DEPENDS += " \
    python3-cffi \
"

RDEPENDS:${PN} += " \
    python3-cffi \
    python3-pycairo \
"

# Python3 compatible only
PYTHON_COMPATIBLE = "python3"

S = "${WORKDIR}/git"
