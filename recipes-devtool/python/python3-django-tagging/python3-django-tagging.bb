DESCRIPTION = "django-tagging is a reusable Django application that provides a simple tagging framework to associate tags with Django models."
HOMEPAGE = "https://github.com/graphite-project/whisper"
LICENSE = "0BSD"
LIC_FILES_CHKSUM = "file://LICENSE.txt;md5=518e3b1a49e75800d11856d1f428a0b3"
SECTION = "devel/python"

SRC_URI = "git://github.com/jazzband/django-tagging.git;branch=develop;protocol=https"
SRCREV = "f3622e62112c3ecc89eabb2512b1b3dd2e5e6ca0"

S = "${WORKDIR}/git"

# Define the Python version this package works with. Adjust if necessary.
inherit allarch setuptools3
