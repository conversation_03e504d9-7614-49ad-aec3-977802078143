[Unit]
Description=[i2R] Service principal de i2R
Requires=embedded-hal.service
After=syslog.target network.target embedded-hal.service

[Service]
SuccessExitStatus=0
User=i2r-system
Group=i2r-system
AmbientCapabilities=CAP_NET_BIND_SERVICE

Type=notify
NotifyAccess=all
Restart=on-failure
WatchdogSec=60s

EnvironmentFile=@APPLICATION_INSTALL_DIR@/i2r-environment
ExecStart=@APPLICATION_INSTALL_DIR@/i2R-wrapper.sh
ExecStop=/bin/kill -15 $MAINPID

[Install]
WantedBy=multi-user.target
