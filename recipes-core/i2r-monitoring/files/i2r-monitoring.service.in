[Unit]
Description=[i2R] Service de monitoring de i2R
Requires=embedded-hal.service
After=syslog.target network.target embedded-hal.service

[Service]
SuccessExitStatus=0
User=i2r-system
Group=i2r-system

Type=notify
NotifyAccess=all
Restart=on-failure
WatchdogSec=60s

EnvironmentFile=@APPLICATION_INSTALL_DIR@/i2r-monitoring-environment
ExecStart=@APPLICATION_INSTALL_DIR@/i2r-monitoring-wrapper.sh
ExecStop=/bin/kill -15 $MAINPID

[Install]
WantedBy=multi-user.target
