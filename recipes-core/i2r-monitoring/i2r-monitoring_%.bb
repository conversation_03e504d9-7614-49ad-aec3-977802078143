DESCRIPTION="i2R Monitoring App"
HOMEPAGE="https://placide.enedis.fr/dev/nexus/ccma/i2r/i2r-monitoring"
LICENSE="CLOSED"
LIC_FILES_CHKSUM=""

# Version du package
PV = "1.0.1"
PACKAGES = "${PN}"

JAVA_VERSION = "21"

DEPENDS = "openjdk-${JAVA_VERSION}-jdk-native i2r-usersgroup "
RDEPENDS:${PN} = " openjdk-${JAVA_VERSION}-jre sqlite-jdbc i2r-usersgroup i2r-modules "

BB_STRICT_CHECKSUM = "0"
SRC_URI = "\
    git://placide.enedis.fr/dev/nexus/ccma/i2r/i2r-monitoring.git;branch=${I2R_MONITORING_GIT_BRANCH};protocol=https;user=${PLACIDE_USER}:${PLACIDE_TOKEN} \
    file://i2r-monitoring.service.in \
    file://i2r-monitoring-environment.in \
    file://i2r-monitoring-wrapper.sh;subdir=${BP} \
    "

SRCREV = "${AUTOREV}"
S = "${WORKDIR}/git"

inherit allarch java-append systemd pkgconfig

PACKAGECONFIG ??= "${@bb.utils.filter('DISTRO_FEATURES', 'systemd', d)}"
PACKAGECONFIG[systemd] = "--with-systemd,--without-systemd,systemd"

# Configuration paths
export JAVA_HOME="${RECIPE_SYSROOT_NATIVE}/usr/lib/jvm/openjdk-${JAVA_VERSION}-jdk"
export APPLICATION_DATA="/var/lib/i2r/"
export APPLICATION_INSTALL_DIR="${datadir}/${PN}"

# Monitoring app jar filename
export I2R_MONITORING_JAR="${PN}-${PV}.jar"

# Main class of monitoring app
export MAIN_CLASS="fr.enedis.i2r.monitoring.main.Main"

do_configure[network] = "1"
do_compile[network] = "1"

do_configure:prepend() {
    cd ${S}
    bbnote "téléchargement de maven par le wrapper"
    ./mvnw --version

    bbnote "Build des dependences locales (metrics, infra)"
    ./mvnw install -pl modules/domain/metrics,modules/infra -DskipTests -DARTIFACTORY_USER=${ARTIFACTORY_USER} -DARTIFACTORY_TOKEN=${ARTIFACTORY_TOKEN}

    bbnote ""
    bbnote "Récupération des dépendences externe sur artifactory"
    ./mvnw org.apache.maven.plugins:maven-dependency-plugin:3.1.1:copy-dependencies \
        -DincludeScope=runtime \
        -DARTIFACTORY_USER=${ARTIFACTORY_USER} \
        -DARTIFACTORY_TOKEN=${ARTIFACTORY_TOKEN}
    install -d ${S}/all-dependencies
    find ${S} -type f -path "*/target/dependency/*.jar" -exec cp {} ${S}/all-dependencies/ \;

    bbnote ""
    bbnote "Construction du class path"
    CLASS_PATH=$(for jar in ${S}/all-dependencies/*.jar ; do echo -n "${APPLICATION_INSTALL_DIR}/$(basename $jar):" ; done)${datadir_java}/sqlite-jdbc.jar:${datadir_java}/${I2R_MONITORING_JAR}:

    cd ${WORKDIR}
    for configfile in *.in ; do
        sed -e "s|@JAVA_HOME@|${JAVA_INSTALL_DIR}|g" \
            -e "s|@APPLICATION_INSTALL_DIR@|$APPLICATION_INSTALL_DIR|g" \
            -e "s|@JAVA_INSTALL_DIR@|${JAVA_INSTALL_DIR}|g" \
            -e "s|@APPLICATION_DATA@|$APPLICATION_DATA|g" \
            -e "s|@CLASS_PATH@|${CLASS_PATH}|g" \
            -e "s|@MAIN_CLASS@|${MAIN_CLASS}|g" \
        ${WORKDIR}/$configfile > ${B}/${configfile%.in}
        bbnote "Generate ${B}/${configfile%.in} with ${JAVA_HOME} ${APPLICATION_INSTALL_DIR} ${JAVA_INSTALL_DIR} ${APPLICATIONDATA}"
    done
}

do_compile() {
    bbnote "Compilation du main de i2r-monitoring"
    ./mvnw -e package -pl modules/domain/metrics,modules/infra,apps/main -am -DskipTests
}

do_install() {
    bbnote "Installation du main de i2r-monitoring"
    oe_jarinstall -g i2r-system -o i2r-system -m u=Xr,g=,o= ${S}/apps/main/target/i2r-monitoring.jar ${I2R_MONITORING_JAR}

    bbnote "Installation des dépendances externes"
    install -g i2r-system -o i2r-system -d ${D}${APPLICATION_INSTALL_DIR}
    for file in ${S}/all-dependencies/*.jar
    do
      install -g i2r-system -o i2r-system -m 400 ${file} ${D}${APPLICATION_INSTALL_DIR}/$(basename $file)
    done

    bbnote "Instalation des fichier de configuration de service i2r-monitoring"
    install -g i2r-system -o i2r-system -m 0700 ${WORKDIR}/${BP}/i2r-monitoring-wrapper.sh ${D}${APPLICATION_INSTALL_DIR}
    if ${@bb.utils.contains('DISTRO_FEATURES', 'systemd', 'true', 'false', d)}; then
       install -d ${D}${systemd_system_unitdir}
       install -m 0600 ${S}/i2r-monitoring.service ${D}${systemd_system_unitdir}/
       install -m 0600 ${S}/i2r-monitoring-environment ${D}${APPLICATION_INSTALL_DIR}
    fi
}

SYSTEMD_PACKAGES = "${@bb.utils.contains('DISTRO_FEATURES','systemd','${PN}','',d)}"
SYSTEMD_SERVICE:${PN} += "${@bb.utils.contains('PACKAGECONFIG', 'systemd', 'i2r-monitoring.service', '', d)}"
SYSTEMD_AUTO_ENABLE:${PN} = "disable"

FILES:${PN}:append = " \
    ${datadir_java}/i2r-monitoring.jar \
    ${datadir_java}/${I2R_MONITORING_JAR} \
    ${datadir}/i2r-monitoring-wrapper.sh \
    ${datadir}/i2r-monitoring-environment \
    ${APPLICATION_INSTALL_DIR}/*.jar \
"
