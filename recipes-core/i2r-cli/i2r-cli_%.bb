DESCRIPTION="i2R Command Line Interface"
HOMEPAGE="https://placide.enedis.fr/dev/nexus/ccma/i2r/i2r-cli"
LICENSE="CLOSED"
LIC_FILES_CHKSUM=""

PV = "1.0.0"
PACKAGES = "${PN}"

inherit cargo cargo-update-recipe-vendored-crates ptest-cargo pkgconfig

# Not supposed to be needed
INSANE_SKIP:${PN} += "already-stripped"

DEPENDS:append = " \
    dbus \
"

# NOTE: I REALLY dislike using tokens here, they are going to be displayed in the logs ...
SRC_URI = "git://placide.enedis.fr/dev/nexus/ccma/i2r/i2r-cli.git;protocol=https;user=${PLACIDE_USER}:${PLACIDE_TOKEN};branch=${I2R_CLI_GIT_BRANCH}"
SRCREV = "${AUTOREV}"

include ${BPN}-crates.inc

S = "${WORKDIR}/git"


do_install:append() {
    # Remove temporary rustlib files (used at build time)
    rm -rf ${D}/usr/lib/rustlib
}
