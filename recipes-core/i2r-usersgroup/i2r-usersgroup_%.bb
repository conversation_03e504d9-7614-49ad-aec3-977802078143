HOMEPAGE = "https://placide.enedis.fr/dev/nexus/ccma/i2r/i2r"
DESCRIPTION = "i2R Application Users"
LICENSE="CLOSED"

PV = "1.0"

inherit allarch useradd

RDEPENDS:${PN} = " sudo "

USERADD_PACKAGES = "${PN}"

GROUPADD_PARAM:${PN} = "-g 2000 i2r-lib; \
                        -g 2001 i2r-system; \
                       "

USERADD_PARAM:${PN} = "-g 2000 -M -u 2000 -p 'x' -r -s /bin/false i2r-lib; \
                       -g 2001 -M -G i2r-lib,sudo -u 2001 -p 'x' -r -s /bin/false i2r-system; \
                      "

do_install() {
    mkdir -p ${D}${sysconfdir}/sudoers.d
    echo 'i2r-system ALL=(root) NOPASSWD: /sbin/reboot' >> ${D}${sysconfdir}/sudoers.d/i2r-system
}
FILES_${PN} += "${sysconfdir}/sudoers.d"
