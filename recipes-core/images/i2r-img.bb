SUMMARY = "I2R Production image"
LICENSE = "MIT"

# Add images features
# Note: It is different from IMAGE_INSTALL, it is like a "macro" that will define several automatically
IMAGE_FEATURES:append = " package-management ssh-server-openssh"

# Note: this has been moved on a separate line in order to be able to comment / uncomment it easily.
# IMAGE_FEATURES:append = " read-only-rootfs"


# Install all required packages

IMAGE_INSTALL:append = " \ 
    openjdk-21-jre \
    dpkg \
    chrony \
    chronyc \
    packagegroup-i2r-test-all \
    i2r-locales \
    polkit \
"

## Useful features to add to the image depending on the needs:
## - To have shell aliases
IMAGE_INSTALL:append = " i2r-aliases "
## - To configure a network interface in order to allow SSH connection
IMAGE_INSTALL:append = " eth0-custom-address "

# By default, the image will be built with a root filesystem (rootfs) that has just a little extra space beyond what is required (30%)
# this can be problematic when we need to manually upload some files for whatever reason, so we increase this free space to 50%
IMAGE_OVERHEAD_FACTOR = "1.5"

inherit core-image

