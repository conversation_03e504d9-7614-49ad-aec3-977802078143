DESCRIPTION = "i2R Packagegroup"
LICENSE = "CLOSED"
LIC_FILES_CHKSUM = ""

inherit packagegroup

PACKAGES = "\
    ${PN}-base \
    ${PN}-supervision \
    ${PN}-all \
    ${PN}-test \
    ${PN}-perf \
    ${PN}-test-all \
"

SUMMARY:${PN}-base = "i2R base applications"
RDEPENDS:${PN}-base = "\
    i2r-app \
    i2r-network \
    i2r-cli \
    i2r-usersgroup \
    logs-folder \
"

SUMMARY:${PN}-supervision = "i2R supervisions stack for production"
RDEPENDS:${PN}-supervision = "\
    i2r-monitoring \
    python3-whisper \
    python3-carbon \
    telegraf \
"

SUMMARY:${PN}-all = "i2R all stack for production"
RDEPENDS:${PN}-all = "\
    ${PN}-base \
    ${PN}-supervision \
"

SUMMARY:${PN}-test = "i2R test tools NOT for production"
RDEPENDS:${PN}-test = "\
    embedded-hal \
    python3-graphite-web \
    i2r-dev-certificates \
    i2r-ca-ccma-recette \
    curl \
    add-ppp0-route \
    tmux \
    less \
    sqlite3 \
    htop \
    minicom \
"
SUMMARY:${PN}-perf = "i2R perf tools NOT for production"
RDEPENDS:${PN}-perf = "\
"

SUMMARY:${PN}-test-all = "i2R all tools NOT for production"
RDEPENDS:${PN}-test-all = "\
    ${PN}-all \
    ${PN}-test \
    ${PN}-perf \
"
