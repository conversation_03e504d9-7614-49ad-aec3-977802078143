DESCRIPTION="i2R Application - compilation et ajout au repository local des modules java de i2R"
HOMEPAGE="https://placide.enedis.fr/dev/nexus/ccma/i2r/i2r"
LICENSE="CLOSED"
LIC_FILES_CHKSUM=""

PV = "1.0.0"
PACKAGES = "${PN}"

JAVA_VERSION = "21"

DEPENDS = "openjdk-${JAVA_VERSION}-jdk-native i2r-usersgroup "
RDEPENDS:${PN} = " openjdk-${JAVA_VERSION}-jre sqlite-jdbc i2r-usersgroup "

BB_STRICT_CHECKSUM = "0"
SRC_URI = "git://placide.enedis.fr/dev/nexus/ccma/i2r/i2r.git;branch=${I2R_GIT_BRANCH};protocol=https;user=${PLACIDE_USER}:${PLACIDE_TOKEN}"

SRCREV = "${AUTOREV}"
S = "${WORKDIR}/git"

inherit allarch java-append pkgconfig

# Configuration paths
export JAVA_HOME="${RECIPE_SYSROOT_NATIVE}/usr/lib/jvm/openjdk-${JAVA_VERSION}-jdk"

do_configure[network] = "1"
do_compile[network] = "1"

do_configure:prepend(){
    cd ${S}
    bbnote "téléchargement de maven par le wrapper"
    ./mvnw --version
}


do_compile() {
    cd ${S}
    ./mvnw install -pl modules/infra,modules/domain/comsi,modules/domain/security,modules/domain/system -DskipTests -DARTIFACTORY_USER=${ARTIFACTORY_USER} -DARTIFACTORY_TOKEN=${ARTIFACTORY_TOKEN}
}

do_install() {
    bbnote "Installation des dépendances interne dans le dossier /usr/share/i2r"
    install -d ${D}${datadir}/i2r
    install -m 0644 ${S}/modules/infra/target/infra-*.jar ${D}${datadir}/i2r
    install -m 0644 ${S}/modules/domain/comsi/target/comsi-*.jar ${D}${datadir}/i2r
    install -m 0644 ${S}/modules/domain/security/target/security-*.jar ${D}${datadir}/i2r
    install -m 0644 ${S}/modules/domain/system/target/system-*.jar ${D}${datadir}/i2r
}
 
FILES:${PN}:append = " ${datadir}/i2r/*.jar "
