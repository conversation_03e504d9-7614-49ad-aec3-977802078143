DESCRIPTION="Sql Lite"
HOMEPAGE=""
LICENSE="CLOSED"
LIC_FILES_CHKSUM=""

PACKAGES = "${PN}"

inherit lib_package java-append

JAVA_VERSION = "21"

DEPENDS = "zip-native openjdk-${JAVA_VERSION}-jdk-native i2r-usersgroup "
RDEPENDS:${PN} = " i2r-usersgroup "

SRC_URI = "https://artifactory-zci.enedis.fr/artifactory/remote-maven-central/org/apache/maven/wrapper/maven-wrapper-distribution/3.3.2/maven-wrapper-distribution-3.3.2-only-script.zip;subdir=${BP};sha256sum=81f6e5505d44263ef4ebf47935dd552c88de7466a6443d8738406fa8f88e0a03 \
           file://maven-wrapper.properties;subdir=${BP}/.mvn//wrapper/ \
           file://maven-settings.xml;subdir=${BP}/.mvn//wrapper/ \
"

COMPATIBLE_HOST = "(x86_64|aarch64).*-linux"

REMOVE_FOLDER:aarch64 = "arm \
                         armv6 \
                         armv7 \
                         ppc64 \
                         riscv64 \
                         x86 \
                         x86_64 \
                        "

REMOVE_FOLDER:x86_64 = "arm \
                         armv6 \
                         armv7 \
                         aarch64 \
                         ppc64 \
                         riscv64 \
                         x86 \
                        "

# Enable network during do_configure in order to fetch maven artifacts
do_configure[network] = "1"

do_configure() {
    export JAVA_HOME=${RECIPE_SYSROOT_NATIVE}/usr/lib/jvm/openjdk-${JAVA_VERSION}-jdk
    $JAVA_HOME/bin/java -version

    bbnote "Get sqllite from Artifactory"
    ./mvnw -s .mvn/wrapper/maven-settings.xml org.apache.maven.plugins:maven-dependency-plugin:3.1.1:copy -DoutputDirectory=./ -Dartifact=org.xerial:${BPN}:${PV}:jar

    for remove in Linux-Android FreeBSD Linux-Musl Mac Windows
    do
        zip ${BPN}-${PV}.jar -d org/sqlite/native/$remove/* || true
    done
    for remove in $REMOVE_FOLDER
    do
        zip ${BPN}-${PV}.jar -d org/sqlite/native/Linux/$remove/* || true
    done
}

do_install() {
    oe_jarinstall -g i2r-lib -o i2r-lib -m u=rX,g=rX,o= ${BPN}-${PV}.jar ${BPN}.jar
}

FILES:${PN}:append = " ${datadir_java}/${BPN}-${PV}.jar ${datadir_java}/${BPN}.jar"
