DESCRIPTION="i2R Mock HAL"
HOMEPAGE="https://placide.enedis.fr/dev/nexus/ccma/i2r/embedded-hal"
LICENSE="CLOSED"
LIC_FILES_CHKSUM=""

PV = "1.0.0"
PACKAGES = "${PN}"

inherit cargo cargo-update-recipe-vendored-crates ptest-cargo pkgconfig systemd

PACKAGECONFIG ??= "${@bb.utils.filter('DISTRO_FEATURES', 'systemd', d)}"
PACKAGECONFIG[systemd] = "--with-systemd,--without-systemd,systemd"

# Not supposed to be needed
INSANE_SKIP:${PN} += "already-stripped"

DEPENDS:append = " \
    dbus \
"

# NOTE: I REALLY dislike using tokens here, they are going to be displayed in the logs ...
SRC_URI = "git://placide.enedis.fr/dev/nexus/ccma/i2r/embedded-hal.git;protocol=https;user=${PLACIDE_USER}:${PLACIDE_TOKEN};branch=${EMBEDDED_HAL_GIT_BRANCH}"
SRCREV = "${AUTOREV}"

SRC_URI += " \
    file://dbus/conf/fr.enedis.HAL.LEDManager1.conf \
    file://dbus/conf/fr.enedis.HAL.ModemManager1.conf \
    file://dbus/conf/fr.enedis.HAL.BoardManager1.conf \
    file://service/embedded-hal.service \
"

include ${BPN}-crates.inc

S = "${WORKDIR}/git"

do_install:append() {
    install -d ${D}${sysconfdir}/dbus-1/system.d
    install -m 0644 ${WORKDIR}/dbus/conf/fr.enedis.HAL.LEDManager1.conf ${D}${sysconfdir}/dbus-1/system.d/
    install -m 0644 ${WORKDIR}/dbus/conf/fr.enedis.HAL.ModemManager1.conf ${D}${sysconfdir}/dbus-1/system.d/
    install -m 0644 ${WORKDIR}/dbus/conf/fr.enedis.HAL.BoardManager1.conf ${D}${sysconfdir}/dbus-1/system.d/

    # Install the systemd service file
    install -d ${D}${systemd_system_unitdir}
    install -m 0644 ${WORKDIR}/service/embedded-hal.service ${D}${systemd_system_unitdir}/

    # Remove temporary rustlib files (used at build time)
    rm -rf ${D}/usr/lib/rustlib
}


SYSTEMD_PACKAGES = "${@bb.utils.contains('DISTRO_FEATURES','systemd','${PN}','',d)}"
SYSTEMD_SERVICE:${PN} += "${@bb.utils.contains('PACKAGECONFIG', 'systemd', 'embedded-hal.service', '', d)}"

FILES:${PN} += " \
    ${sysconfdir}/dbus-1/system.d/fr.enedis.HAL.LEDManager1.conf \
    ${sysconfdir}/dbus-1/system.d/fr.enedis.HAL.ModemManager1.conf \
    ${sysconfdir}/dbus-1/system.d/fr.enedis.HAL.BoardManager1.conf \
    ${systemd_system_unitdir}/embedded-hal.service \
"

# Ensure the service is enabled by default
SYSTEMD_SERVICE:${PN} = "embedded-hal.service"
