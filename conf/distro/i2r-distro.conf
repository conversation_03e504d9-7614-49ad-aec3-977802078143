include conf/distro/poky.conf

DISTRO = "i2r-distro"
DISTRO_NAME = "i2r-distro"
DISTRO_VERSION = "1.0"

# Use systemd instead of poky's default sysvinit
INIT_MANAGER = "systemd"

# Use deb as package format (other possibilities are rpm and ipk)
PACKAGE_CLASSES = "package_deb"

# Include IPSec support for VPN connectivity using Strongswan
# TODO: I2R-666 - Uncomment when ipsec needs to be implemented
# DISTRO_FEATURES:append = " ipsec"

# Enable IPV4 support
DISTRO_FEATURES:append = " ipv4"

# Enable IPV6 Support
# TODO: I2R-667 - Uncomment when IPV6 needs to be supported
# DISTRO_FEATURES:append = " ipv6"

# Add support for ldconfig utility (used for dynamic linking of shared libraries)
DISTRO_FEATURES:append = " ldconfig"

# Add PPP dialup (modem) support
DISTRO_FEATURES:append = " ppp"

# Include support for Security-Enhanced Linux (SELinux)
# /!\ WARNING /! \ : requires meta-selinux
# TODO: I2R-669 - Uncomment when SELinux needs to be implemented
# DISTRO_FEATURES:append = " selinux"

# Merges the /bin, /sbin, /lib, and /lib64 directories into their respective
# counterparts in the /usr directory to provide better package and application compatibility.
DISTRO_FEATURES:append = " usrmerge"

# Include polkit support
DISTRO_FEATURES:append = " polkit"

# As we use Chrony, remove timesyncd to free up some space
PACKAGECONFIG:remove:pn-systemd = "timesyncd"