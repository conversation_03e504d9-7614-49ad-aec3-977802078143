###!/bin/sh
#
#   Rename this file to ppp_on_boot and pppd will be fired up as
#   soon as the system comes up, connecting to `provider'.
#
#   If you also make this file executable, and replace the first line
#   with just "#!/bin/sh", the commands below will be executed instead.
#

# The location of the ppp daemon itself (shouldn't need to be changed)
PPPD=/usr/sbin/pppd

# The default provider to connect to
$PPPD call provider

# Additional connections, which would just use settings from
# /etc/ppp/options.<tty>
#$PPPD ttyS0
#$PPPD ttyS1
#$PPPD ttyS2
#$PPPD ttyS3
