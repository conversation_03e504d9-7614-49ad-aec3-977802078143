# Use custom NTP servers
# CRI sur PACY
server *********** iburst
# CRI sur NOE
server *********** iburst
# IODE sur PACY
server ************ iburst
# IODE sur NOE
server ************ iburst
# Serveur du conteneur docker (projet crony-serveur)
# server ******** iburst

# Allow NTP clients from local network
allow ***********/24

# conf de l'UDP socket,  pour permettre à telegraf de récuperer les métriques
allow 127.0.0.1
bindcmdaddress 127.0.0.1

# Specify drift file
driftfile /var/lib/chrony/chrony.drift

# Enable RTC synchronization
rtcfile /var/lib/chrony/chrony.rtc

#limite déclanchant la correction <écart déclancheur en seconde de l'ajustement> <nombre d'update>
makestep 1.0 3
